{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"ix": {"title": "Effect Index", "description": "Effect Index. Used for expressions.", "type": "number"}, "mn": {"title": "Match Name", "description": "After Effect's Match Name. Used for expressions.", "type": "string"}, "nm": {"title": "Name", "description": "After Effect's Name. Used for expressions.", "type": "string"}, "ty": {"title": "Type", "description": "Effect type.", "type": "number", "const": 5}, "ef": {"title": "Effects", "description": "Effect List of properties.", "items": {"$ref": "#/effects/index", "type": "object"}, "type": "array"}, "en": {"description": "Enabled AE property value", "extended_name": "Enabled", "type": "number"}}}
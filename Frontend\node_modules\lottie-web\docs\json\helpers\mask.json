{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"inv": {"title": "Inverted", "description": "Inverted Mask flag", "type": "boolean", "default": false}, "nm": {"title": "Name", "description": "Mask name. Used for expressions and effects.", "type": "string"}, "pt": {"title": "Points", "description": "Mask vertices", "oneOf": [{"$ref": "#/properties/shape"}, {"$ref": "#/properties/shapeKeyframed"}], "type": "object"}, "o": {"title": "Opacity", "description": "Mask opacity.", "type": "object", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "default": {"a": 0, "k": 100}}, "mode": {"title": "Mode", "description": "Mask mode. Not all mask types are supported.", "type": "string", "oneOf": [{"standsFor": "None", "const": "n"}, {"standsFor": "Additive", "const": "a"}, {"standsFor": "Subtract", "const": "s"}, {"standsFor": "Intersect", "const": "i"}, {"standsFor": "Lighten", "const": "l"}, {"standsFor": "Darken", "const": "d"}, {"standsFor": "Difference", "const": "f"}], "default": "a"}}}